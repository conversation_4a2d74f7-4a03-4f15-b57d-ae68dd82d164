package com.example.habits9.ui.details

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.NotificationsOff
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import android.widget.Toast
import com.example.habits9.data.analytics.TimePeriod
import com.example.habits9.ui.details.components.CompletionHistoryChart
import com.example.habits9.ui.details.components.CompletionCalendarHeatmap

// Design System Colors - Theme-aware (from style_habits9.md)
// Using Material 3 theme colors for proper light/dark theme support

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitDetailsScreen(
    habitId: Long,
    onBackClick: () -> Unit = {},
    onEditClick: (Long, com.example.habits9.data.HabitType) -> Unit = { _, _ -> },
    onDeleteClick: () -> Unit = {},
    viewModel: HabitDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    var showDropdownMenu by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    // Load habit details when the screen is first displayed
    LaunchedEffect(habitId) {
        android.util.Log.d("HabitDetailsScreen", "Loading habit details for habitId: $habitId")
        viewModel.loadHabitDetails(habitId)
    }

    // Show error if there's an issue loading data
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            Toast.makeText(context, "Error loading habit: $error", Toast.LENGTH_LONG).show()
            android.util.Log.e("HabitDetailsScreen", "Error loading habit: $error")
        }
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when {
                            uiState.isLoading -> "Loading..."
                            uiState.error != null -> "Error"
                            uiState.habit != null -> uiState.habit?.name ?: "Habit Details"
                            else -> "Habit Details"
                        },
                        color = MaterialTheme.colorScheme.onBackground,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.SemiBold // display-small style
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }
                },
                actions = {
                    // Edit button
                    IconButton(onClick = {
                        uiState.habit?.let { habit ->
                            onEditClick(habit.id, habit.habitType)
                        }
                    }) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }

                    // Kebab menu (overflow menu)
                    IconButton(onClick = { showDropdownMenu = true }) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "More options",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }

                    // Dropdown menu
                    DropdownMenu(
                        expanded = showDropdownMenu,
                        onDismissRequest = { showDropdownMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("Delete") },
                            onClick = {
                                showDropdownMenu = false
                                showDeleteDialog = true
                            }
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.background,
                    titleContentColor = MaterialTheme.colorScheme.onBackground,
                    navigationIconContentColor = MaterialTheme.colorScheme.onBackground,
                    actionIconContentColor = MaterialTheme.colorScheme.onBackground
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // Sub-Header Row (Subtitle section) - show loading state or data
            if (uiState.isLoading) {
                // Show loading placeholder for subtitle
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Loading...",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 10.sp,
                        fontFamily = FontFamily.Monospace,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = "Loading...",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 10.sp,
                        fontFamily = FontFamily.Monospace,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "Loading...",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 10.sp,
                        fontFamily = FontFamily.Monospace,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End
                    )
                }
            } else {
                SubHeaderRow(
                    frequencyText = uiState.frequencyText,
                    weekNumber = uiState.weekNumber,
                    formattedDate = uiState.formattedDate
                )
            }

            Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide

            // Overview Section
            OverviewSection()

            Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide

            // Analytics Charts Section
            if (!uiState.isLoading && uiState.habit != null) {
                AnalyticsChartsSection(
                    uiState = uiState,
                    onTimePeriodChanged = { timePeriod ->
                        uiState.habit?.let { habit ->
                            viewModel.updateChartTimePeriod(habit.id, habit.habitType, timePeriod)
                        }
                    }
                )
            }
        }
    }

    // Delete confirmation dialog
    if (showDeleteDialog) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = "Delete Habit?",
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete this habit? All its history will be permanently lost. This action cannot be undone.",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 12.sp
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showDeleteDialog = false
                        uiState.habit?.let { habit ->
                            viewModel.deleteHabit(habit.id) {
                                onDeleteClick()
                            }
                        }
                    }
                ) {
                    Text(
                        text = "Delete",
                        color = androidx.compose.ui.graphics.Color.Red,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text(
                        text = "Cancel",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 12.sp
                    )
                }
            },
            containerColor = MaterialTheme.colorScheme.surface
        )
    }
}

@Composable
fun SubHeaderRow(
    frequencyText: String = "",
    weekNumber: Int = 0,
    formattedDate: String = ""
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left-aligned: Frequency with icon
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = "Frequency",
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = frequencyText.ifEmpty { "Daily" },
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 10.sp,
                fontFamily = FontFamily.Monospace,
                fontWeight = FontWeight.Normal
            )
        }

        // Center-aligned: Week number with icon
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Schedule,
                contentDescription = "Week",
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = if (weekNumber > 0) "W$weekNumber" else "W34",
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 10.sp,
                fontFamily = FontFamily.Monospace,
                fontWeight = FontWeight.Normal
            )
        }

        // Right-aligned: Current date with icon
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.End
        ) {
            Icon(
                imageVector = Icons.Default.DateRange,
                contentDescription = "Date",
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = formattedDate.ifEmpty { "Sat, 09 Aug" },
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 10.sp,
                fontFamily = FontFamily.Monospace,
                fontWeight = FontWeight.Normal
            )
        }
    }
}

@Composable
fun OverviewSection() {
    Column {
        // Section Title
        Text(
            text = "Overview",
            color = MaterialTheme.colorScheme.onBackground,
            fontSize = 14.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.SemiBold // display-small style
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Metrics Row - Using weighted layout for proportional sizing
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center
        ) {
            // Score - wider space (weight 5)
            Box(
                modifier = Modifier.weight(5f),
                contentAlignment = Alignment.Center
            ) {
                MetricView(
                    value = "19%",
                    label = "Score",
                    progress = 0.19f
                )
            }

            // Month - narrower space (weight 4)
            Box(
                modifier = Modifier.weight(4f),
                contentAlignment = Alignment.Center
            ) {
                MetricView(
                    value = "+11%",
                    label = "Month",
                    progress = 0.11f
                )
            }

            // Year - narrower space (weight 4)
            Box(
                modifier = Modifier.weight(4f),
                contentAlignment = Alignment.Center
            ) {
                MetricView(
                    value = "+19%",
                    label = "Year",
                    progress = 0.19f
                )
            }

            // Total - standard space (weight 4)
            Box(
                modifier = Modifier.weight(4f),
                contentAlignment = Alignment.Center
            ) {
                MetricView(
                    value = "7",
                    label = "Total",
                    progress = null // No progress ring for Total
                )
            }
        }
    }
}

@Composable
fun MetricView(
    value: String,
    label: String,
    progress: Float? = null
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Progress Indicator (if provided)
        if (progress != null) {
            CircularProgressIndicator(
                progress = { progress },
                modifier = Modifier.size(24.dp),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 2.dp,
                trackColor = MaterialTheme.colorScheme.outline
            )
        } else {
            // Empty space to maintain alignment
            Spacer(modifier = Modifier.size(24.dp))
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Value
        Text(
            text = value,
            color = MaterialTheme.colorScheme.onBackground,
            fontSize = 14.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.SemiBold // display-small style
        )

        Spacer(modifier = Modifier.height(4.dp))

        // Label
        Text(
            text = label,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            fontSize = 10.sp,
            fontFamily = FontFamily.Monospace,
            fontWeight = FontWeight.Normal // label-small style
        )
    }
}

@Composable
fun AnalyticsChartsSection(
    uiState: HabitDetailsUiState,
    onTimePeriodChanged: (TimePeriod) -> Unit
) {
    Column {
        // Completion History Chart
        when {
            uiState.yesNoAnalytics != null -> {
                CompletionHistoryChart(
                    chartData = uiState.yesNoAnalytics.completionHistory,
                    selectedTimePeriod = uiState.currentTimePeriod,
                    onTimePeriodChanged = onTimePeriodChanged,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide

                CompletionCalendarHeatmap(
                    calendarData = uiState.yesNoAnalytics.calendarData.mapValues { it.value as Any },
                    isYesNoHabit = true,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            uiState.measurableAnalytics != null -> {
                CompletionHistoryChart(
                    chartData = uiState.measurableAnalytics.completionHistory,
                    selectedTimePeriod = uiState.currentTimePeriod,
                    onTimePeriodChanged = onTimePeriodChanged,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide

                CompletionCalendarHeatmap(
                    calendarData = uiState.measurableAnalytics.calendarData.mapValues { it.value as Any },
                    isYesNoHabit = false,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}