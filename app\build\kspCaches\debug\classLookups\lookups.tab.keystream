  Activity android.app  Context android.content  ContextWrapper android.content  Build 
android.os  
VERSION_CODES android.os.Build  ContextThemeWrapper android.view  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  CompletionRepository com.example.habits9.data  Habit com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSectionRepository com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  	Companion -com.example.habits9.data.CompletionRepository  	Companion (com.example.habits9.data.HabitRepository  	Companion /com.example.habits9.data.HabitSectionRepository  HabitAnalyticsRepository "com.example.habits9.data.analytics  HabitAnalyticsUseCase "com.example.habits9.data.analytics  DatabaseModule com.example.habits9.di  
MainViewModel com.example.habits9.ui  
AuthViewModel com.example.habits9.ui.auth  CreateHabitViewModel "com.example.habits9.ui.createhabit  HabitDetailsViewModel com.example.habits9.ui.details  HabitReorderViewModel #com.example.habits9.ui.habitreorder  ManageSectionsViewModel %com.example.habits9.ui.managesections  SettingsViewModel com.example.habits9.ui.settings  MainActivity com.example.uhabits_99                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      