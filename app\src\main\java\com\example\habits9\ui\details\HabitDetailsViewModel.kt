package com.example.habits9.ui.details

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitType
import com.example.habits9.data.UserPreferencesRepository
import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.FrequencyType
import com.example.habits9.data.DayOfWeek
import com.example.habits9.data.analytics.GeneralInfo
import com.example.habits9.data.analytics.HabitAnalyticsUseCase
import com.example.habits9.data.analytics.MeasurableHabitAnalytics
import com.example.habits9.data.analytics.TimePeriod
import com.example.habits9.data.analytics.YesNoHabitAnalytics
import com.example.habits9.ui.MainViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject

/**
 * ViewModel for habit details screen.
 * Provides habit data, analytics, and UI state management.
 */
@HiltViewModel
class HabitDetailsViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val completionRepository: CompletionRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val analyticsUseCase: HabitAnalyticsUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(HabitDetailsUiState())
    val uiState: StateFlow<HabitDetailsUiState> = _uiState.asStateFlow()

    private val _generalInfo = MutableStateFlow<GeneralInfo?>(null)
    val generalInfo: StateFlow<GeneralInfo?> = _generalInfo.asStateFlow()

    init {
        loadGeneralInfo()
    }

    /**
     * Load habit details and analytics for a specific habit.
     */
    fun loadHabitDetails(habitId: Long) {
        viewModelScope.launch {
            android.util.Log.d("HabitDetailsViewModel", "Starting to load habit details for habitId: $habitId")
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // Debug: List all available habits first
                val allHabits = habitRepository.getAllHabits().first()
                android.util.Log.d("HabitDetailsViewModel", "Available habits: ${allHabits.map { "${it.name} (id: ${it.id})" }}")

                // Get habit data with timeout
                android.util.Log.d("HabitDetailsViewModel", "Fetching habit from repository...")
                val habit = withTimeoutOrNull(10000) { // 10 second timeout
                    habitRepository.getHabitById(habitId).first()
                }

                if (habit == null) {
                    android.util.Log.w("HabitDetailsViewModel", "Habit with id $habitId not found or timed out")
                    // Provide fallback data to show the interface working
                    val firstDayOfWeek = userPreferencesRepository.firstDayOfWeek.first()
                    val today = LocalDate.now()
                    val weekNumber = MainViewModel.WeekBoundaryUtils.getWeekNumber(today, firstDayOfWeek)
                    val formattedDate = formatCurrentDate(today)

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        habit = Habit(
                            id = habitId,
                            name = "Sample Habit", // Fallback name
                            description = "This is a sample habit for demonstration"
                        ),
                        frequencyText = "Daily",
                        weekNumber = weekNumber,
                        formattedDate = formattedDate,
                        error = "Using sample data - habit $habitId not found"
                    )
                    return@launch
                }

                android.util.Log.d("HabitDetailsViewModel", "Habit loaded: ${habit.name}")
                
                // Get user preferences for week calculation
                val firstDayOfWeek = userPreferencesRepository.firstDayOfWeek.first()
                
                // Calculate current date and week info
                val today = LocalDate.now()
                val weekNumber = MainViewModel.WeekBoundaryUtils.getWeekNumber(today, firstDayOfWeek)
                val formattedDate = formatCurrentDate(today)
                val frequencyText = formatFrequencyText(habit)
                
                // Load analytics based on habit type
                when (habit.habitType) {
                    HabitType.YES_NO -> {
                        val analytics = loadYesNoHabitAnalytics(habitId)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            habit = habit,
                            frequencyText = frequencyText,
                            weekNumber = weekNumber,
                            formattedDate = formattedDate,
                            yesNoAnalytics = analytics,
                            measurableAnalytics = null
                        )
                    }
                    HabitType.NUMERICAL -> {
                        val analytics = loadMeasurableHabitAnalytics(habitId)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            habit = habit,
                            frequencyText = frequencyText,
                            weekNumber = weekNumber,
                            formattedDate = formattedDate,
                            yesNoAnalytics = null,
                            measurableAnalytics = analytics
                        )
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HabitDetailsViewModel", "Error loading habit details for habitId: $habitId", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }

    /**
     * Load analytics for a specific habit.
     */
    fun loadHabitAnalytics(habitId: Long, habitType: HabitType) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                when (habitType) {
                    HabitType.YES_NO -> {
                        val analytics = loadYesNoHabitAnalytics(habitId)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            yesNoAnalytics = analytics,
                            measurableAnalytics = null
                        )
                    }
                    HabitType.NUMERICAL -> {
                        val analytics = loadMeasurableHabitAnalytics(habitId)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            yesNoAnalytics = null,
                            measurableAnalytics = analytics
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }

    /**
     * Load analytics for Yes/No habits.
     */
    private suspend fun loadYesNoHabitAnalytics(habitId: Long): YesNoHabitAnalytics {
        val currentStreak = analyticsUseCase.getCurrentStreak(habitId)
        val longestStreak = analyticsUseCase.getLongestStreak(habitId)
        val completionRate = analyticsUseCase.getCompletionRate(habitId)
        val totalCompletions = analyticsUseCase.getTotalCompletions(habitId)
        val completionHistory = analyticsUseCase.getCompletionHistory(habitId, TimePeriod.MONTH)
        val calendarData = analyticsUseCase.getCalendarData(habitId)
            .mapValues { (_, value) -> value as Boolean }

        return YesNoHabitAnalytics(
            currentStreak = currentStreak,
            longestStreak = longestStreak,
            completionRate = completionRate,
            totalCompletions = totalCompletions,
            completionHistory = completionHistory,
            calendarData = calendarData
        )
    }

    /**
     * Load analytics for measurable habits.
     */
    private suspend fun loadMeasurableHabitAnalytics(habitId: Long): MeasurableHabitAnalytics {
        val currentStreak = analyticsUseCase.getCurrentStreak(habitId)
        val longestStreak = analyticsUseCase.getLongestStreak(habitId)
        val completionRate = analyticsUseCase.getCompletionRate(habitId)
        val totalCompletions = analyticsUseCase.getTotalCompletions(habitId)
        val totalAmount = analyticsUseCase.getTotalAmount(habitId)
        val averagePerCompletion = analyticsUseCase.getAveragePerCompletion(habitId)
        val bestDay = analyticsUseCase.getBestDay(habitId)
        val completionHistory = analyticsUseCase.getCompletionHistory(habitId, TimePeriod.MONTH)
        val calendarData = analyticsUseCase.getCalendarData(habitId)
            .mapValues { (_, value) -> value as Float }

        return MeasurableHabitAnalytics(
            currentStreak = currentStreak,
            longestStreak = longestStreak,
            completionRate = completionRate,
            totalCompletions = totalCompletions,
            totalAmount = totalAmount,
            averagePerCompletion = averagePerCompletion,
            bestDay = bestDay,
            completionHistory = completionHistory,
            calendarData = calendarData
        )
    }

    /**
     * Load general information.
     */
    private fun loadGeneralInfo() {
        _generalInfo.value = analyticsUseCase.getGeneralInfo()
    }

    /**
     * Refresh analytics data for the current habit.
     */
    fun refreshAnalytics(habitId: Long, habitType: HabitType) {
        loadHabitAnalytics(habitId, habitType)
    }

    /**
     * Update chart time period and reload data.
     */
    fun updateChartTimePeriod(habitId: Long, habitType: HabitType, timePeriod: TimePeriod) {
        viewModelScope.launch {
            try {
                val completionHistory = analyticsUseCase.getCompletionHistory(habitId, timePeriod)

                when (habitType) {
                    HabitType.YES_NO -> {
                        _uiState.value.yesNoAnalytics?.let { analytics ->
                            val updatedAnalytics = analytics.copy(completionHistory = completionHistory)
                            _uiState.value = _uiState.value.copy(
                                yesNoAnalytics = updatedAnalytics,
                                currentTimePeriod = timePeriod
                            )
                        }
                    }
                    HabitType.NUMERICAL -> {
                        _uiState.value.measurableAnalytics?.let { analytics ->
                            val updatedAnalytics = analytics.copy(completionHistory = completionHistory)
                            _uiState.value = _uiState.value.copy(
                                measurableAnalytics = updatedAnalytics,
                                currentTimePeriod = timePeriod
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "Error updating chart data"
                )
            }
        }
    }

    /**
     * Helper function to format current date.
     */
    private fun formatCurrentDate(date: LocalDate): String {
        val formatter = DateTimeFormatter.ofPattern("EEE, dd MMM", Locale.getDefault())
        return date.format(formatter)
    }

    /**
     * Helper function to format frequency text based on habit data.
     */
    private fun formatFrequencyText(habit: Habit): String {
        return when (habit.frequencyTypeEnum) {
            FrequencyType.DAILY -> {
                if (habit.repeatsEvery == 1) "Daily" else "Every ${habit.repeatsEvery} days"
            }
            FrequencyType.WEEKLY -> {
                if (habit.daysOfWeek.isEmpty()) {
                    if (habit.repeatsEvery == 1) "Weekly" else "Every ${habit.repeatsEvery} weeks"
                } else {
                    val dayNames = habit.daysOfWeek.map { dayValue ->
                        DayOfWeek.fromValue(dayValue.toInt())?.shortName ?: ""
                    }.filter { it.isNotEmpty() }
                    dayNames.joinToString(", ")
                }
            }
            FrequencyType.MONTHLY -> {
                if (habit.repeatsEvery == 1) "Monthly" else "Every ${habit.repeatsEvery} months"
            }
        }
    }

    /**
     * Delete a habit and all its completion records
     */
    fun deleteHabit(habitId: Long, onSuccess: () -> Unit) {
        viewModelScope.launch {
            try {
                // Get the habit first to pass to deleteHabit method
                val habit = habitRepository.getHabitById(habitId).first()

                // First delete all completions for this habit
                completionRepository.deleteAllCompletionsForHabit(habitId)

                // Then delete the habit itself
                habitRepository.deleteHabit(habit)

                // Call success callback
                onSuccess()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete habit: ${e.message}"
                )
            }
        }
    }
}

/**
 * UI state for habit details screen.
 */
data class HabitDetailsUiState(
    val isLoading: Boolean = false,
    val habit: Habit? = null,
    val frequencyText: String = "",
    val weekNumber: Int = 0,
    val formattedDate: String = "",
    val yesNoAnalytics: YesNoHabitAnalytics? = null,
    val measurableAnalytics: MeasurableHabitAnalytics? = null,
    val currentTimePeriod: TimePeriod = TimePeriod.MONTH,
    val error: String? = null
)

/**
 * Legacy UI state for habit analytics screen (kept for backward compatibility).
 */
data class HabitAnalyticsUiState(
    val isLoading: Boolean = false,
    val yesNoAnalytics: YesNoHabitAnalytics? = null,
    val measurableAnalytics: MeasurableHabitAnalytics? = null,
    val error: String? = null
)
