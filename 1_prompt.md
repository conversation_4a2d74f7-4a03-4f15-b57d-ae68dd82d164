# Prompt 5.5: Refine and Fix Habit Details Layout

## A. The Objective & Context

The goal of this task is to fix the layout and improve the readability of the **Habit Details** screen. The current implementation, as seen in `67.jpg`, has spacing, alignment, and text-wrapping issues that make the data difficult to read.

This is a **UI refinement task**. You will adjust the existing XML layouts to ensure all components are well-spaced and their content is clearly visible, adhering strictly to the `style_habits9.md` design system.

## B. Detailed Implementation Plan

### 1. Refactor the "Overview" Section for Proportional Sizing
- **Problem**: The four items in the "Overview" card ("Score", "Month", "Year", "Total") are currently forced into equal-width containers, causing the text to be cut off.
- **Solution**:
    - The `LinearLayout` that contains these four items must be replaced with a `ConstraintLayout`.
    - Use `ConstraintLayout` chains or guidelines to give the items proportional widths. The "Score" and "Total" items should be wider, while the "Month" and "Year" items can be narrower. The goal is to give each item just enough space for its content to be fully visible without truncation.
    - Ensure the text for the metric values (e.g., "19%", "+11%") and the labels below them ("Score", "Month") are center-aligned within their respective containers.

### 2. Improve Readability of the Completion History Chart
- **Problem**: The labels on the x-axis of the bar chart (e.g., "W20", "W21", "W22") are too close together, making them unreadable.
- **Solution**:
    - Modify the logic that populates the chart's x-axis.
    - Implement a rule to show fewer labels. For example, display a label for every 2nd or 3rd week, rather than for every week. This will create more space and make the visible labels legible.

### 3. Standardize Vertical Spacing
- **Problem**: The vertical spacing between the main sections ("Overview", "Completion History", "Completion Calendar") is inconsistent.
- **Solution**:
    - Review the root layout file for the screen.
    - Apply a consistent vertical margin between each of the main content cards. Use the `space-lg` (16dp) token from `style_habits9.md` for this purpose. This will create a clean, visually balanced separation between sections.

## C. Meticulous Verification Plan

1.  **Overview Layout Verification**:
    - **CRITICAL**: Launch the details screen. Verify that all text within the "Overview" card is fully visible and not cut off or wrapped awkwardly. The layout should look balanced and easy to read.
    - Confirm that the four items are no longer taking up equal space, but have widths appropriate to their content.

2.  **Chart Readability Verification**:
    - **CRITICAL**: Examine the "Completion History" bar chart. Verify that the labels on the x-axis are spaced out and clearly legible, with no text overlapping.

3.  **Spacing Verification**:
    - **CRITICAL**: Visually confirm that there is consistent and adequate vertical spacing between the "Overview" card, the "Completion History" card, and the "Completion Calendar" card.

4.  **Theme Verification**:
    - Switch the device between **Light and Dark modes**. Verify that the corrected layout looks perfect and remains readable in both themes.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**. [cite: 0_promptGuidelines.md]
- It is the **single source of truth** for styling decisions. [cite: 0_promptGuidelines.md]

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory). [cite: 0_promptGuidelines.md]
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. [cite: 0_promptGuidelines.md]
- The reference project serves as a **blueprint** for implementation. [cite: 0_promptGuidelines.md]
- This step is mandatory. **Do not proceed to implementation without this step.** [cite: 0_promptGuidelines.md]

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured. [cite: 0_promptGuidelines.md]
- Even for new features, existing components or utility functions may be reusable. [cite: 0_promptGuidelines.md]
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code. [cite: 0_promptGuidelines.md]

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development. [cite: 0_promptGuidelines.md]
- Keep the codebase **organized and clutter-free**. [cite: 0_promptGuidelines.md]

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions. [cite: 0_promptGuidelines.md]
- Immediately pause and seek clarification either from the project lead or directly from me. [cite: 0_promptGuidelines.md]
- It is better to get clarity than to redo or fix avoidable mistakes later. [cite: 0_promptGuidelines.md]

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use. [cite: 0_promptGuidelines.md]
- This includes obsolete routes, modules, features, or legacy logic. [cite: 0_promptGuidelines.md]